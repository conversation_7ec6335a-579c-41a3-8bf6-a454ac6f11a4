<template>
  <div class="test-container">
    <h2>VisualHeader 组件测试</h2>
    
    <!-- 测试组件 -->
    <div class="header-test">
      <VisualHeader @material-change="handleMaterialChange" @area-change="handleAreaChange" />
    </div>
    
    <!-- 显示选择结果 -->
    <div class="result-display">
      <h3>选择结果：</h3>
      
      <div class="result-item">
        <h4>物资种类：</h4>
        <p><strong>值：</strong>{{ materialResult.value }}</p>
        <p><strong>文本：</strong>{{ materialResult.text }}</p>
      </div>
      
      <div class="result-item">
        <h4>省市区：</h4>
        <p><strong>值：</strong>{{ areaResult.value }}</p>
        <p><strong>文本：</strong>{{ areaResult.text }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import VisualHeader from './components/VisualHeader.vue';

  // 存储选择结果
  const materialResult = ref({
    value: [],
    text: ''
  });

  const areaResult = ref({
    value: [],
    text: ''
  });

  // 处理物资类型变化
  const handleMaterialChange = (value: string[], text: string) => {
    console.log('物资类型变化:', { value, text });
    materialResult.value = { value, text };
  };

  // 处理省市区变化
  const handleAreaChange = (value: string[], text: string) => {
    console.log('省市区变化:', { value, text });
    areaResult.value = { value, text };
  };
</script>

<style lang="less" scoped>
  .test-container {
    padding: 20px;
    background: #f5f5f5;
    min-height: 100vh;
  }

  h2 {
    color: #333;
    margin-bottom: 20px;
  }

  .header-test {
    background: #0a0e27;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
  }

  .result-display {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);

    h3 {
      color: #333;
      margin-bottom: 20px;
      border-bottom: 2px solid #13e6db;
      padding-bottom: 10px;
    }

    .result-item {
      margin-bottom: 20px;
      padding: 15px;
      background: #f9f9f9;
      border-radius: 6px;

      h4 {
        color: #13e6db;
        margin-bottom: 10px;
      }

      p {
        margin: 5px 0;
        color: #666;

        strong {
          color: #333;
        }
      }
    }
  }
</style>
