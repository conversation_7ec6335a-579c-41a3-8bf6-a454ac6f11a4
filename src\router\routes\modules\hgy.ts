import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';

const hgy: AppRouteModule = {
  path: '/hgy',
  name: 'Hgy',
  component: LAYOUT,
  redirect: '/hgy/visual',
  meta: {
    orderNo: 15,
    icon: 'ion:bar-chart-outline',
    title: '灰谷网',
  },
  children: [
    {
      path: 'visual',
      name: 'HgyVisual',
      component: () => import('/@/views/hgy/visual/index.vue'),
      meta: {
        title: '数据可视化大屏',
        icon: 'ion:analytics-outline',
      },
    },
  ],
};

export default hgy;
