import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';

const hgy: AppRouteModule = {
  path: '/hgy',
  name: 'Hgy',
  component: LAYOUT,
  redirect: '/hgy/visual',
  meta: {
    orderNo: 15,
    icon: 'ion:bar-chart-outline',
    title: '灰谷网',
  },
  children: [
    {
      path: 'visual',
      name: 'HgyVisual',
      component: () => import('/@/views/hgy/visual/index.vue'),
      meta: {
        title: '数据可视化大屏',
        icon: 'ion:analytics-outline',
      },
    },
    {
      path: 'test-header',
      name: 'HgyTestHeader',
      component: () => import('/@/views/hgy/visual/test-header.vue'),
      meta: {
        title: '头部组件测试',
        icon: 'ion:bug-outline',
        hideMenu: true, // 隐藏菜单，仅用于测试
      },
    },
  ],
};

export default hgy;
