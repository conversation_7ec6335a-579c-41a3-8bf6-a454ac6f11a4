<template>
  <div class="test-animation">
    <h2>排名组件动画测试</h2>
    
    <div class="test-controls">
      <a-button type="primary" @click="reloadComponents">重新加载动画</a-button>
    </div>
    
    <div class="components-container">
      <div class="component-wrapper">
        <h3>标的数据排名</h3>
        <TargetRanking v-if="showTargetRanking" />
      </div>
      
      <div class="component-wrapper">
        <h3>资产处置数据排名</h3>
        <AssetRanking v-if="showAssetRanking" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import TargetRanking from './components/TargetRanking.vue';
  import AssetRanking from './components/AssetRanking.vue';

  const showTargetRanking = ref(true);
  const showAssetRanking = ref(true);

  // 重新加载组件以测试动画
  const reloadComponents = () => {
    showTargetRanking.value = false;
    showAssetRanking.value = false;
    
    setTimeout(() => {
      showTargetRanking.value = true;
      showAssetRanking.value = true;
    }, 100);
  };
</script>

<style lang="less" scoped>
  .test-animation {
    padding: 20px;
    background: #0a0e27;
    min-height: 100vh;
    color: white;
  }

  h2 {
    color: #13e6db;
    text-align: center;
    margin-bottom: 20px;
  }

  .test-controls {
    text-align: center;
    margin-bottom: 30px;
  }

  .components-container {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
  }

  .component-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;

    h3 {
      color: #13e6db;
      margin: 0;
    }
  }

  :deep(.ant-btn-primary) {
    background: linear-gradient(135deg, #13e6db 0%, #0bc4d4 100%);
    border-color: #13e6db;
    
    &:hover {
      background: linear-gradient(135deg, #0bc4d4 0%, #13e6db 100%);
      box-shadow: 0 0 10px rgba(19, 230, 219, 0.5);
    }
  }
</style>
