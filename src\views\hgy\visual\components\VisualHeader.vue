<template>
  <div class="visual-header">
    <!-- 头部内容 -->
    <div class="header-content">
      <!-- 左侧按钮 -->
      <div class="header-left">
        <!-- 物资种类选择 -->
        <div class="nav-button material-select">
          <a-cascader
            v-model:value="selectedMaterial"
            :options="materialTypeOptions"
            placeholder="请选择物资种类"
            show-search
            :field-names="{ label: 'name', value: 'id', children: 'children' }"
            change-on-select
            :filter-option="filterMaterialType"
            @change="handleMaterialChange"
            :dropdown-style="{ background: 'linear-gradient(135deg, #0a0e27 0%, #1a1a3a 100%)', border: '1px solid #13e6db' }"
          >
            <div class="button-text">{{ selectedMaterialText || '请选择物资种类' }}</div>
          </a-cascader>
        </div>
        <!-- 省市区选择 -->
        <div class="nav-button address-select">
          <a-cascader
            v-model:value="selectedArea"
            :options="areaOptions"
            placeholder="选择省市区"
            @change="handleAreaChange"
            :dropdown-style="{ background: 'linear-gradient(135deg, #0a0e27 0%, #1a1a3a 100%)', border: '1px solid #13e6db' }"
          >
            <div class="button-text">{{ selectedAreaText || '选择省市区' }}</div>
          </a-cascader>
        </div>
      </div>

      <!-- 中间标题 -->
      <div class="header-center">
        <h1 class="main-title">灰谷网经营数据可视化大屏</h1>
      </div>

      <!-- 右侧信息 -->
      <div class="header-right">
        <div class="time-info">{{ currentTime }}</div>
        <!-- 暂时隐藏返回灰谷云按钮 -->
        <!-- <div class="nav-button return-button">
          <span class="button-text">返回灰谷云</span>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, computed } from 'vue';
  import dayjs from 'dayjs';
  import { getMaterialTree } from '@/api/supplyAndDemand/SupplyDemand';
  import type { MaterialTypeNode } from '@/api/supplyAndDemand/SupplyDemand';
  import { regionData } from '/@/components/Form/src/utils/areaDataUtil';

  // 定义事件
  const emit = defineEmits<{
    materialChange: [value: string[], text: string];
    areaChange: [value: string[], text: string];
  }>();

  const currentTime = ref('');

  // 物资种类相关
  const selectedMaterial = ref<string[]>([]);
  const materialTypeOptions = ref<MaterialTypeNode[]>([]);

  // 省市区相关
  const selectedArea = ref<string[]>([]);
  const areaOptions = ref(regionData);

  // 计算显示文本
  const selectedMaterialText = computed(() => {
    if (selectedMaterial.value && selectedMaterial.value.length > 0) {
      // 根据选中的ID找到对应的名称
      const findMaterialName = (options: MaterialTypeNode[], ids: string[]): string => {
        if (!ids || ids.length === 0) return '';

        for (const option of options) {
          if (option.id === ids[0]) {
            if (ids.length === 1) {
              return option.name;
            } else if (option.children && ids.length > 1) {
              const childName = findMaterialName(option.children, ids.slice(1));
              return childName ? `${option.name} / ${childName}` : option.name;
            }
            return option.name;
          }
        }
        return '';
      };

      return findMaterialName(materialTypeOptions.value, selectedMaterial.value);
    }
    return '';
  });

  const selectedAreaText = computed(() => {
    if (selectedArea.value && selectedArea.value.length > 0) {
      // 使用JeecgBoot的区域数据工具来获取名称
      const getAreaNames = (codes: string[]): string => {
        const names: string[] = [];
        for (const code of codes) {
          const area = regionData.find((item) => item.value === code);
          if (area) {
            names.push(area.label);
          }
        }
        return names.join(' / ');
      };

      return getAreaNames(selectedArea.value);
    }
    return '';
  });

  // 更新时间
  const updateTime = () => {
    currentTime.value = dayjs().format('上午HH:mm YYYY年MM月DD日 dddd');
  };

  // 获取物资类型树形数据
  const fetchMaterialTypeTree = async () => {
    try {
      const result = await getMaterialTree();
      console.log('获取物资类型树形数据:', result);
      if (result && Array.isArray(result)) {
        materialTypeOptions.value = result;
      }
    } catch (error) {
      console.error('获取物资类型失败:', error);
    }
  };

  // 物资类型搜索过滤
  const filterMaterialType = (inputValue: string, path: any[]) => {
    return path.some((option) => option.name.toLowerCase().includes(inputValue.toLowerCase()));
  };

  // 物资选择变化处理
  const handleMaterialChange = (value: string[]) => {
    selectedMaterial.value = value;
    console.log('选中的物资类型:', value);
    // 发射物资变化事件
    emit('materialChange', value, selectedMaterialText.value);
  };

  // 省市区选择变化处理
  const handleAreaChange = (value: string[]) => {
    selectedArea.value = value;
    console.log('选中的省市区:', value);
    // 发射省市区变化事件
    emit('areaChange', value, selectedAreaText.value);
  };

  let timeInterval: NodeJS.Timeout;

  onMounted(() => {
    updateTime();
    // 每秒更新时间
    timeInterval = setInterval(updateTime, 1000);
    // 获取物资类型数据
    fetchMaterialTypeTree();
  });

  onUnmounted(() => {
    if (timeInterval) {
      clearInterval(timeInterval);
    }
  });
</script>

<style lang="less" scoped>
  .visual-header {
    background: url('@/assets/visual/header.png') no-repeat center center;
    background-size: cover;
    height: 98px;
    width: 100%;
  }

  .header-content {
    position: relative;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 2;
    padding: 0 20px;
  }

  .header-left {
    display: flex;
    gap: 20px;
  }

  .header-center {
    .main-title {
      color: #ffffff;
      font-size: 42px;
      font-weight: bold;
      margin: 0;
      text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
      background: linear-gradient(135deg, #ffffff 0%, #12e6db 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-top: 10px;
    }
  }

  .header-right {
    display: flex;
    gap: 20px;
    align-items: center;

    .time-info {
      color: #13e6db !important;
      font-size: 16px;
      font-weight: 400;
      text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
    }
  }

  .nav-button {
    position: relative;
    width: 135px;
    height: 35px;
    cursor: pointer;
    transition: all 0.3s ease;

    background: linear-gradient(135deg, #13e6db 0%, #0bc4d4 50%, #13e6db 100%);

    clip-path: polygon(0 0, ~'calc(100% - 8px)' 0, 100% 8px, 100% 100%, 8px 100%, 0 ~'calc(100% - 8px)');

    /* 外发光效果 */
    box-shadow:
      0 0 10px rgba(19, 230, 219, 0.4),
      0 0 20px rgba(19, 230, 219, 0.2);

    /* 添加发光动画 */
    animation: buttonGlow 3s ease-in-out infinite;

    &:hover {
      transform: translateY(-2px);
      box-shadow:
        0 0 15px rgba(19, 230, 219, 0.6),
        0 0 25px rgba(19, 230, 219, 0.3);

      .button-text {
        background: linear-gradient(135deg, rgba(0, 76, 102, 0.9) 0%, rgba(0, 60, 80, 1) 50%, rgba(0, 40, 60, 0.9) 100%);

        box-shadow:
          inset 0 0 15px rgba(19, 230, 219, 0.4),
          inset 0 0 25px rgba(19, 230, 219, 0.2);
      }
    }

    .button-text {
      position: absolute;
      top: 2px;
      left: 2px;
      right: 2px;
      bottom: 2px;

      /* 内部容器背景 */
      background: linear-gradient(135deg, rgba(0, 76, 102, 0.8) 0%, rgba(0, 60, 80, 0.9) 50%, rgba(0, 40, 60, 0.8) 100%);

      /* 内部容器的切角效果（比外部小2px） */
      clip-path: polygon(0 0, ~'calc(100% - 6px)' 0, 100% 6px, 100% 100%, 6px 100%, 0 ~'calc(100% - 6px)');

      /* 内发光效果 */
      box-shadow:
        inset 0 0 10px rgba(19, 230, 219, 0.3),
        inset 0 0 20px rgba(19, 230, 219, 0.1);

      /* 文字内容样式 */
      color: #ffffff;
      font-size: 14px;
      font-weight: 400;
      text-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
    }

    /* 不同按钮的特殊尺寸 */
    &.address-select {
      width: 225px;
    }

    /* 级联选择器样式重置 */
    :deep(.ant-cascader) {
      width: 100%;
      height: 100%;

      .ant-select-selector {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
        padding: 0 !important;
        height: 100% !important;

        .ant-select-selection-search {
          display: none !important;
        }

        .ant-select-selection-item,
        .ant-select-selection-placeholder {
          display: none !important;
        }
      }

      .ant-select-arrow {
        display: none !important;
      }
    }
  }

  /* 发光动画效果 */
  @keyframes buttonGlow {
    0% {
      box-shadow:
        0 0 10px rgba(19, 230, 219, 0.4),
        0 0 20px rgba(19, 230, 219, 0.2);
    }
    50% {
      box-shadow:
        0 0 15px rgba(19, 230, 219, 0.6),
        0 0 25px rgba(19, 230, 219, 0.3);
    }
    100% {
      box-shadow:
        0 0 10px rgba(19, 230, 219, 0.4),
        0 0 20px rgba(19, 230, 219, 0.2);
    }
  }

  /* 级联选择器下拉框样式 */
  :deep(.ant-cascader-dropdown) {
    background: linear-gradient(135deg, #0a0e27 0%, #1a1a3a 100%) !important;
    border: 1px solid #13e6db !important;
    border-radius: 8px !important;
    box-shadow: 0 0 20px rgba(19, 230, 219, 0.3) !important;

    .ant-cascader-menu {
      background: transparent !important;
      border-right: 1px solid rgba(19, 230, 219, 0.2) !important;

      .ant-cascader-menu-item {
        color: #ffffff !important;
        background: transparent !important;
        border-bottom: 1px solid rgba(19, 230, 219, 0.1) !important;

        &:hover {
          background: rgba(19, 230, 219, 0.1) !important;
          color: #13e6db !important;
        }

        &.ant-cascader-menu-item-active {
          background: rgba(19, 230, 219, 0.2) !important;
          color: #13e6db !important;
        }

        &.ant-cascader-menu-item-selected {
          background: rgba(19, 230, 219, 0.3) !important;
          color: #13e6db !important;
          font-weight: bold !important;
        }
      }
    }

    .ant-cascader-menu:last-child {
      border-right: none !important;
    }
  }

  /* 搜索框样式 */
  :deep(.ant-cascader-dropdown .ant-input) {
    background: rgba(19, 230, 219, 0.1) !important;
    border: 1px solid rgba(19, 230, 219, 0.3) !important;
    color: #ffffff !important;

    &::placeholder {
      color: rgba(255, 255, 255, 0.6) !important;
    }

    &:focus {
      border-color: #13e6db !important;
      box-shadow: 0 0 5px rgba(19, 230, 219, 0.5) !important;
    }
  }
</style>
